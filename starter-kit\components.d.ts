/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AddAuthenticatorAppDialog: typeof import('./resources/ts/components/dialogs/AddAuthenticatorAppDialog.vue')['default']
    AddEditAddressDialog: typeof import('./resources/ts/components/dialogs/AddEditAddressDialog.vue')['default']
    AddEditPermissionDialog: typeof import('./resources/ts/components/dialogs/AddEditPermissionDialog.vue')['default']
    AddEditRoleDialog: typeof import('./resources/ts/components/dialogs/AddEditRoleDialog.vue')['default']
    AddPaymentMethodDialog: typeof import('./resources/ts/components/dialogs/AddPaymentMethodDialog.vue')['default']
    AppAutocomplete: typeof import('./resources/ts/@core/components/app-form-elements/AppAutocomplete.vue')['default']
    AppBarSearch: typeof import('./resources/ts/@core/components/AppBarSearch.vue')['default']
    AppCardActions: typeof import('./resources/ts/@core/components/cards/AppCardActions.vue')['default']
    AppCardCode: typeof import('./resources/ts/@core/components/cards/AppCardCode.vue')['default']
    AppCombobox: typeof import('./resources/ts/@core/components/app-form-elements/AppCombobox.vue')['default']
    AppDateTimePicker: typeof import('./resources/ts/@core/components/app-form-elements/AppDateTimePicker.vue')['default']
    AppDrawerHeaderSection: typeof import('./resources/ts/@core/components/AppDrawerHeaderSection.vue')['default']
    AppLoadingIndicator: typeof import('./resources/ts/components/AppLoadingIndicator.vue')['default']
    AppPricing: typeof import('./resources/ts/components/AppPricing.vue')['default']
    AppSearchHeader: typeof import('./resources/ts/components/AppSearchHeader.vue')['default']
    AppSelect: typeof import('./resources/ts/@core/components/app-form-elements/AppSelect.vue')['default']
    AppStepper: typeof import('./resources/ts/@core/components/AppStepper.vue')['default']
    AppTextarea: typeof import('./resources/ts/@core/components/app-form-elements/AppTextarea.vue')['default']
    AppTextField: typeof import('./resources/ts/@core/components/app-form-elements/AppTextField.vue')['default']
    BoutiqueCrudDialog: typeof import('./resources/ts/components/admin/boutiques/BoutiqueCrudDialog.vue')['default']
    BoutiqueViewDialog: typeof import('./resources/ts/components/admin/boutiques/BoutiqueViewDialog.vue')['default']
    Breadcrumbs: typeof import('./resources/ts/components/common/Breadcrumbs.vue')['default']
    BuyNow: typeof import('./resources/ts/@core/components/BuyNow.vue')['default']
    CardAddEditDialog: typeof import('./resources/ts/components/dialogs/CardAddEditDialog.vue')['default']
    CardStatisticsHorizontal: typeof import('./resources/ts/@core/components/cards/CardStatisticsHorizontal.vue')['default']
    CardStatisticsVertical: typeof import('./resources/ts/@core/components/cards/CardStatisticsVertical.vue')['default']
    CardStatisticsVerticalSimple: typeof import('./resources/ts/@core/components/CardStatisticsVerticalSimple.vue')['default']
    CategoryCrudDialog: typeof import('./resources/ts/components/admin/categories/CategoryCrudDialog.vue')['default']
    CategoryImageUpload: typeof import('./resources/ts/components/admin/categories/CategoryImageUpload.vue')['default']
    CategoryViewDialog: typeof import('./resources/ts/components/admin/categories/CategoryViewDialog.vue')['default']
    ConfirmDialog: typeof import('./resources/ts/components/dialogs/ConfirmDialog.vue')['default']
    ConfirmModal: typeof import('./resources/ts/components/common/ConfirmModal.vue')['default']
    CreateAppDialog: typeof import('./resources/ts/components/dialogs/CreateAppDialog.vue')['default']
    CustomCheckboxes: typeof import('./resources/ts/@core/components/app-form-elements/CustomCheckboxes.vue')['default']
    CustomCheckboxesWithIcon: typeof import('./resources/ts/@core/components/app-form-elements/CustomCheckboxesWithIcon.vue')['default']
    CustomCheckboxesWithImage: typeof import('./resources/ts/@core/components/app-form-elements/CustomCheckboxesWithImage.vue')['default']
    CustomizerSection: typeof import('./resources/ts/@core/components/CustomizerSection.vue')['default']
    CustomRadios: typeof import('./resources/ts/@core/components/app-form-elements/CustomRadios.vue')['default']
    CustomRadiosWithIcon: typeof import('./resources/ts/@core/components/app-form-elements/CustomRadiosWithIcon.vue')['default']
    CustomRadiosWithImage: typeof import('./resources/ts/@core/components/app-form-elements/CustomRadiosWithImage.vue')['default']
    DataTable: typeof import('./resources/ts/components/common/DataTable.vue')['default']
    DialogCloseBtn: typeof import('./resources/ts/@core/components/DialogCloseBtn.vue')['default']
    DropZone: typeof import('./resources/ts/@core/components/DropZone.vue')['default']
    EnableOneTimePasswordDialog: typeof import('./resources/ts/components/dialogs/EnableOneTimePasswordDialog.vue')['default']
    ErrorHeader: typeof import('./resources/ts/components/ErrorHeader.vue')['default']
    FileUploader: typeof import('./resources/ts/components/FileUploader.vue')['default']
    I18n: typeof import('./resources/ts/@core/components/I18n.vue')['default']
    LanguageSwitcher: typeof import('./resources/ts/components/LanguageSwitcher.vue')['default']
    MoreBtn: typeof import('./resources/ts/@core/components/MoreBtn.vue')['default']
    Notifications: typeof import('./resources/ts/@core/components/Notifications.vue')['default']
    PaymentProvidersDialog: typeof import('./resources/ts/components/dialogs/PaymentProvidersDialog.vue')['default']
    PricingPlanDialog: typeof import('./resources/ts/components/dialogs/PricingPlanDialog.vue')['default']
    ProductDescriptionEditor: typeof import('./resources/ts/@core/components/ProductDescriptionEditor.vue')['default']
    ProductForm: typeof import('./resources/ts/components/admin/products/ProductForm.vue')['default']
    ProductImageGallery: typeof import('./resources/ts/components/admin/ProductImageGallery.vue')['default']
    ProductImageManager: typeof import('./resources/ts/components/admin/products/ProductImageManager.vue')['default']
    ProductVariantManager: typeof import('./resources/ts/components/admin/products/ProductVariantManager.vue')['default']
    ProductVideoManager: typeof import('./resources/ts/components/admin/products/ProductVideoManager.vue')['default']
    ProfileImageUpload: typeof import('./resources/ts/components/ProfileImageUpload.vue')['default']
    PropositionCards: typeof import('./resources/ts/components/public/PropositionCards.vue')['default']
    PublicImageGallery: typeof import('./resources/ts/components/public/PublicImageGallery.vue')['default']
    PublicVideoGallery: typeof import('./resources/ts/components/public/PublicVideoGallery.vue')['default']
    ReferAndEarnDialog: typeof import('./resources/ts/components/dialogs/ReferAndEarnDialog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ScrollToTop: typeof import('./resources/ts/@core/components/ScrollToTop.vue')['default']
    ShareProjectDialog: typeof import('./resources/ts/components/dialogs/ShareProjectDialog.vue')['default']
    Shortcuts: typeof import('./resources/ts/@core/components/Shortcuts.vue')['default']
    StockIssues: typeof import('./resources/ts/components/public/StockIssues.vue')['default']
    TablePagination: typeof import('./resources/ts/@core/components/TablePagination.vue')['default']
    TheCustomizer: typeof import('./resources/ts/@core/components/TheCustomizer.vue')['default']
    ThemeSwitcher: typeof import('./resources/ts/@core/components/ThemeSwitcher.vue')['default']
    TiptapEditor: typeof import('./resources/ts/@core/components/TiptapEditor.vue')['default']
    TwoFactorAuthDialog: typeof import('./resources/ts/components/dialogs/TwoFactorAuthDialog.vue')['default']
    UserInfoEditDialog: typeof import('./resources/ts/components/dialogs/UserInfoEditDialog.vue')['default']
    UserUpgradePlanDialog: typeof import('./resources/ts/components/dialogs/UserUpgradePlanDialog.vue')['default']
    VariantChips: typeof import('./resources/ts/components/public/VariantChips.vue')['default']
  }
}
