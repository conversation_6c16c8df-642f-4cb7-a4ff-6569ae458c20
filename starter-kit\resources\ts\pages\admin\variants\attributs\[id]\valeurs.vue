<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useApi } from '@/composables/useApi'

// Page meta
definePage({
  meta: {
    action: 'read',
    subject: 'Admin',
    requiresAuth: true,
  },
})

// Types
interface VariantAttribut {
  id: string
  code: string
  nom: string
  actif: boolean
}

interface VariantValeur {
  id: string
  attribut_id: string
  code: string
  libelle: string
  actif: boolean
  ordre: number
}

// Composables
const route = useRoute()
const router = useRouter()

// State
const attribut = ref<VariantAttribut | null>(null)
const valeurs = ref<VariantValeur[]>([])
const loading = ref(false)
const searchQuery = ref('')
const showCreateDialog = ref(false)
const showEditDialog = ref(false)
const showDeleteDialog = ref(false)
const selectedValeur = ref<VariantValeur | null>(null)
const isDeleting = ref(false)

// Form data
const formData = ref({
  code: '',
  libelle: '',
  actif: true,
  ordre: 0
})

const formErrors = ref<Record<string, string[]>>({})

// Computed
const attributId = computed(() => route.params.id as string)

const filteredValeurs = computed(() => {
  if (!searchQuery.value) return valeurs.value
  
  const query = searchQuery.value.toLowerCase()
  return valeurs.value.filter(val => 
    val.code.toLowerCase().includes(query) ||
    val.libelle.toLowerCase().includes(query)
  )
})

// Methods
const fetchAttribut = async () => {
  try {
    const { data, error } = await useApi(`/admin/variant-attributs/${attributId.value}`)
    
    if (!error.value && data.value) {
      const response = data.value as any
      if (response.success) {
        attribut.value = response.data
      }
    }
  } catch (err) {
    console.error('Error fetching variant attribute:', err)
  }
}

const fetchValeurs = async () => {
  try {
    loading.value = true
    const { data, error } = await useApi(`/admin/variant-attributs/${attributId.value}/valeurs`)
    
    if (!error.value && data.value) {
      const response = data.value as any
      if (response.success) {
        valeurs.value = response.data
      }
    }
  } catch (err) {
    console.error('Error fetching variant values:', err)
  } finally {
    loading.value = false
  }
}

const handleCreate = () => {
  const maxOrdre = valeurs.value.length > 0 ? Math.max(...valeurs.value.map(v => v.ordre)) : 0
  formData.value = {
    code: '',
    libelle: '',
    actif: true,
    ordre: maxOrdre + 1
  }
  formErrors.value = {}
  showCreateDialog.value = true
}

const handleEdit = (valeur: VariantValeur) => {
  selectedValeur.value = valeur
  formData.value = {
    code: valeur.code,
    libelle: valeur.libelle,
    actif: valeur.actif,
    ordre: valeur.ordre
  }
  formErrors.value = {}
  showEditDialog.value = true
}

const handleDelete = (valeur: VariantValeur) => {
  selectedValeur.value = valeur
  showDeleteDialog.value = true
}

const submitCreate = async () => {
  try {
    formErrors.value = {}
    const { data, error } = await useApi(`/admin/variant-attributs/${attributId.value}/valeurs`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(formData.value)
    })
    
    if (!error.value && data.value) {
      const response = data.value as any
      if (response.success) {
        valeurs.value.push(response.data)
        valeurs.value.sort((a, b) => a.ordre - b.ordre)
        showCreateDialog.value = false
      } else {
        if (response.errors) {
          formErrors.value = response.errors
        }
      }
    }
  } catch (err) {
    console.error('Error creating variant value:', err)
  }
}

const submitEdit = async () => {
  if (!selectedValeur.value) return
  
  try {
    formErrors.value = {}
    const { data, error } = await useApi(`/admin/variant-attributs/${attributId.value}/valeurs/${selectedValeur.value.id}`, {
      method: 'PUT',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(formData.value)
    })
    
    if (!error.value && data.value) {
      const response = data.value as any
      if (response.success) {
        const index = valeurs.value.findIndex(v => v.id === selectedValeur.value!.id)
        if (index > -1) {
          valeurs.value[index] = response.data
        }
        valeurs.value.sort((a, b) => a.ordre - b.ordre)
        showEditDialog.value = false
      } else {
        if (response.errors) {
          formErrors.value = response.errors
        }
      }
    }
  } catch (err) {
    console.error('Error updating variant value:', err)
  }
}

const confirmDelete = async () => {
  if (!selectedValeur.value) return
  
  try {
    isDeleting.value = true
    const { data, error } = await useApi(`/admin/variant-attributs/${attributId.value}/valeurs/${selectedValeur.value.id}`, {
      method: 'DELETE'
    })
    
    if (!error.value && data.value) {
      const response = data.value as any
      if (response.success) {
        valeurs.value = valeurs.value.filter(v => v.id !== selectedValeur.value!.id)
        showDeleteDialog.value = false
      }
    }
  } catch (err) {
    console.error('Error deleting variant value:', err)
  } finally {
    isDeleting.value = false
  }
}

const goBack = () => {
  router.push('/admin/variants/attributs')
}

// Lifecycle
onMounted(() => {
  fetchAttribut()
  fetchValeurs()
})
</script>

<template>
  <div>
    <!-- Breadcrumbs -->
    <VBreadcrumbs
      :items="[
        { title: 'Admin', disabled: true },
        { title: 'Variants', disabled: true },
        { title: 'Attributes', to: '/admin/variants/attributs' },
        { title: attribut?.nom || 'Values', disabled: true }
      ]"
      class="px-0"
    />

    <!-- Page Header -->
    <div class="d-flex justify-space-between align-center mb-6">
      <div>
        <div class="d-flex align-center gap-3 mb-2">
          <VBtn
            icon="tabler-arrow-left"
            variant="text"
            @click="goBack"
          />
          <h1 class="text-h4 font-weight-bold">
            {{ attribut?.nom }} Values
          </h1>
        </div>
        <p class="text-body-1 text-medium-emphasis">
          Manage values for the "{{ attribut?.nom }}" attribute
        </p>
      </div>
      
      <VBtn
        color="primary"
        prepend-icon="tabler-plus"
        @click="handleCreate"
      >
        Add Value
      </VBtn>
    </div>

    <!-- Search -->
    <VCard class="mb-6">
      <VCardText>
        <VTextField
          v-model="searchQuery"
          placeholder="Search values..."
          prepend-inner-icon="tabler-search"
          clearable
          variant="outlined"
          density="compact"
        />
      </VCardText>
    </VCard>

    <!-- Values List -->
    <VCard>
      <VCardText>
        <div v-if="loading" class="text-center py-8">
          <VProgressCircular indeterminate color="primary" />
        </div>
        
        <div v-else-if="filteredValeurs.length === 0" class="text-center py-8">
          <VIcon icon="tabler-list" size="64" color="grey" class="mb-4" />
          <h3 class="text-h6 mb-2">No values found</h3>
          <p class="text-body-2 text-medium-emphasis">
            {{ searchQuery ? 'Try adjusting your search' : 'Create your first value for this attribute' }}
          </p>
        </div>
        
        <VRow v-else>
          <VCol
            v-for="valeur in filteredValeurs"
            :key="valeur.id"
            cols="12"
            sm="6"
            md="4"
            lg="3"
          >
            <VCard
              class="value-card h-100"
              :class="{ 'opacity-60': !valeur.actif }"
              elevation="2"
            >
              <VCardText class="pb-2">
                <div class="d-flex justify-space-between align-start mb-3">
                  <div>
                    <h4 class="text-h6 font-weight-bold">{{ valeur.libelle }}</h4>
                    <p class="text-caption text-medium-emphasis">{{ valeur.code }}</p>
                    <VChip size="x-small" variant="outlined" class="mt-1">
                      Order: {{ valeur.ordre }}
                    </VChip>
                  </div>
                  <VChip
                    :color="valeur.actif ? 'success' : 'error'"
                    size="small"
                    variant="flat"
                  >
                    {{ valeur.actif ? 'Active' : 'Inactive' }}
                  </VChip>
                </div>
              </VCardText>
              
              <VCardActions class="pt-0">
                <VBtn
                  icon="tabler-edit"
                  size="small"
                  variant="text"
                  @click="handleEdit(valeur)"
                />
                <VBtn
                  icon="tabler-trash"
                  size="small"
                  variant="text"
                  color="error"
                  @click="handleDelete(valeur)"
                />
              </VCardActions>
            </VCard>
          </VCol>
        </VRow>
      </VCardText>
    </VCard>

    <!-- Create Dialog -->
    <VDialog v-model="showCreateDialog" max-width="500">
      <VCard>
        <VCardTitle>Create Value</VCardTitle>
        <VCardText>
          <VForm @submit.prevent="submitCreate">
            <VTextField
              v-model="formData.code"
              label="Code"
              placeholder="e.g., l, blue, cotton"
              :error-messages="formErrors.code"
              variant="outlined"
              class="mb-4"
              required
            />
            
            <VTextField
              v-model="formData.libelle"
              label="Label"
              placeholder="e.g., Large, Blue, Cotton"
              :error-messages="formErrors.libelle"
              variant="outlined"
              class="mb-4"
              required
            />
            
            <VTextField
              v-model.number="formData.ordre"
              label="Order"
              type="number"
              :error-messages="formErrors.ordre"
              variant="outlined"
              class="mb-4"
            />
            
            <VCheckbox
              v-model="formData.actif"
              label="Active"
            />
          </VForm>
        </VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn @click="showCreateDialog = false">Cancel</VBtn>
          <VBtn color="primary" @click="submitCreate">Create</VBtn>
        </VCardActions>
      </VCard>
    </VDialog>

    <!-- Edit Dialog -->
    <VDialog v-model="showEditDialog" max-width="500">
      <VCard>
        <VCardTitle>Edit Value</VCardTitle>
        <VCardText>
          <VForm @submit.prevent="submitEdit">
            <VTextField
              v-model="formData.code"
              label="Code"
              :error-messages="formErrors.code"
              variant="outlined"
              class="mb-4"
              required
            />
            
            <VTextField
              v-model="formData.libelle"
              label="Label"
              :error-messages="formErrors.libelle"
              variant="outlined"
              class="mb-4"
              required
            />
            
            <VTextField
              v-model.number="formData.ordre"
              label="Order"
              type="number"
              :error-messages="formErrors.ordre"
              variant="outlined"
              class="mb-4"
            />
            
            <VCheckbox
              v-model="formData.actif"
              label="Active"
            />
          </VForm>
        </VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn @click="showEditDialog = false">Cancel</VBtn>
          <VBtn color="primary" @click="submitEdit">Update</VBtn>
        </VCardActions>
      </VCard>
    </VDialog>

    <!-- Delete Dialog -->
    <VDialog v-model="showDeleteDialog" max-width="400">
      <VCard>
        <VCardTitle>Delete Value</VCardTitle>
        <VCardText>
          Are you sure you want to delete the value "{{ selectedValeur?.libelle }}"?
          This action cannot be undone.
        </VCardText>
        <VCardActions>
          <VSpacer />
          <VBtn @click="showDeleteDialog = false">Cancel</VBtn>
          <VBtn
            color="error"
            :loading="isDeleting"
            @click="confirmDelete"
          >
            Delete
          </VBtn>
        </VCardActions>
      </VCard>
    </VDialog>
  </div>
</template>

<style scoped>
.value-card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.value-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
</style>
