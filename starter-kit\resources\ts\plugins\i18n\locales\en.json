{
  // ===========================================
  // NAVIGATION & MENU ITEMS
  // ===========================================
  "nav_dashboard": "Dashboard",
  "nav_user_management": "User Management",
  "nav_all_users": "All Users",
  "nav_roles_permissions": "Roles & Permissions",
  "nav_kyc_documents": "KYC Documents",
  "nav_affiliate_management": "Affiliate Management",
  "nav_all_affiliates": "All Affiliates",
  "nav_affiliate_tiers": "Affiliate Tiers",
  "nav_order_management": "Order Management",
  "nav_all_orders": "All Orders",
  "nav_order_conflicts": "Order Conflicts",
  "nav_product_management": "Product Management",
  "nav_products": "Products",
  "nav_categories": "Categories",
  "nav_boutiques": "Boutiques",
  "nav_financial_management": "Financial Management",
  "nav_commissions": "Commissions",
  "nav_payments": "Payments",
  "nav_reports_analytics": "Reports & Analytics",
  "nav_sales_reports": "Sales Reports",
  "nav_affiliate_performance": "Affiliate Performance",
  "nav_my_orders": "My Orders",
  "nav_my_commissions": "My Commissions",
  "nav_marketing_materials": "Marketing Materials",

  // ===========================================
  // PAGE TITLES & HEADERS
  // ===========================================
  "title_admin_dashboard": "Admin Dashboard",
  "title_affiliate_dashboard": "Affiliate Dashboard",
  "title_simple_admin_dashboard": "Simple Admin Dashboard",
  "title_user_management": "User Management",
  "title_roles_permissions": "Roles & Permissions",
  "title_kyc_documents": "KYC Documents",
  "title_my_commissions": "My Commissions",
  "title_marketing_materials": "Marketing Materials",
  "title_my_orders": "My Orders",
  "title_all_affiliates": "All Affiliates",
  "title_affiliate_tiers": "Affiliate Tiers",
  "title_statistics": "Statistics",

  // ===========================================
  // COMMON ACTIONS & BUTTONS
  // ===========================================
  "action_login": "Login",
  "action_logout": "Logout",
  "action_save": "Save",
  "action_cancel": "Cancel",
  "action_delete": "Delete",
  "action_edit": "Edit",
  "action_create": "Create",
  "action_update": "Update",
  "action_view": "View",
  "action_download": "Download",
  "action_upload": "Upload",
  "action_search": "Search",
  "action_filter": "Filter",
  "action_clear": "Clear",
  "action_submit": "Submit",
  "action_reset": "Reset",
  "action_add": "Add",
  "action_remove": "Remove",
  "action_manage": "Manage",
  "action_refresh": "Refresh",

  // ===========================================
  // QUICK ACTIONS
  // ===========================================
  "quick_actions": "Quick Actions",
  "create_order": "Create Order",
  "order_history": "Order History",
  "view_commissions": "Commissions",
  "view_marketing_materials": "Marketing Materials",
  "manage_users": "Manage Users",
  "manage_affiliates": "Manage Affiliates",
  "order_management": "Order Management",
  "reports": "Reports",

  // ===========================================
  // STATISTICS & METRICS
  // ===========================================
  "stats_sales": "Sales",
  "stats_customers": "Customers",
  "stats_products": "Products",
  "stats_revenue": "Revenue",
  "stats_total_affiliates": "Total Affiliates",
  "stats_total_orders": "Total Orders",
  "stats_pending_orders": "Pending Orders",
  "stats_my_orders_count": "My Orders",
  "stats_pending_orders_count": "Pending Orders",
  "stats_total_earnings": "Total Earnings",
  "stats_this_month_earnings": "This Month Earnings",
  "stats_earnings": "Earnings",
  "stats_profit": "Profit",
  "stats_expense": "Expense",
  "stats_updated_ago": "Updated {time} ago",

  // ===========================================
  // TABLE HEADERS & COLUMNS
  // ===========================================
  "table_order_id": "Order ID",
  "table_product": "Product",
  "table_customer": "Customer",
  "table_status": "Status",
  "table_commission": "Commission",
  "table_name": "Name",
  "table_email": "Email",
  "table_phone": "Phone",
  "table_role": "Role",
  "table_kyc_status": "KYC Status",
  "table_created": "Created",
  "table_actions": "Actions",
  "table_recent_orders": "Recent Orders",

  // ===========================================
  // STATUS LABELS
  // ===========================================
  "status_active": "Active",
  "status_inactive": "Inactive",
  "status_pending": "Pending",
  "status_delivered": "Delivered",
  "status_shipped": "Shipped",
  "status_cancelled": "Cancelled",
  "status_blocked": "Blocked",
  "status_current": "Current",
  "status_professional": "Professional",
  "status_rejected": "Rejected",
  "status_resigned": "Resigned",
  "status_applied": "Applied",
  "status_coming_soon": "Coming Soon",

  // ===========================================
  // FORM LABELS & PLACEHOLDERS
  // ===========================================
  "form_full_name": "Full Name",
  "form_email": "Email",
  "form_password": "Password",
  "form_confirm_password": "Confirm Password",
  "form_phone": "Phone",
  "form_address": "Address",
  "form_role": "Role",
  "form_status": "Status",
  "form_kyc_status": "KYC Status",
  "form_document_type": "Document Type",
  "form_file": "File",
  "form_reason": "Reason",
  "form_search": "Search",
  "form_filter_by_role": "Filter by role",
  "form_filter_by_status": "Filter by status",

  "placeholder_enter_full_name": "Enter full name",
  "placeholder_enter_email": "Enter email",
  "placeholder_enter_password": "············",
  "placeholder_enter_phone": "Enter phone number",
  "placeholder_enter_address": "Enter address",
  "placeholder_search_users": "Search users...",
  "placeholder_select_role": "Select role",
  "placeholder_select_status": "Select status",
  "placeholder_email_or_username": "Email or Username",

  // ===========================================
  // AUTHENTICATION & LOGIN
  // ===========================================
  "login_title": "Welcome to {title}! 👋🏻",
  "login_subtitle": "Please sign-in to your account and start the adventure",
  "demo_credentials": "Demo Credentials:",
  "admin_credentials": "Admin: admin / password",
  "affiliate_credentials": "Affiliate: affiliate / password",
  "remember_me": "Remember me",
  "forgot_password": "Forgot Password?",
  "login_failed": "Login failed",
  "new_on_platform": "New on our platform?",
  "create_account": "Create an account",
  "or": "or",

  // ===========================================
  // WELCOME & GREETING MESSAGES
  // ===========================================
  "welcome_message": "Welcome",
  "welcome_admin": "Welcome, {name}! 👋",
  "welcome_affiliate": "Welcome, {name}! 🚀",
  "welcome_simple": "Welcome, {name}!",
  "dashboard_subtitle_admin": "Manage your affiliate platform efficiently",
  "dashboard_subtitle_affiliate": "Track your orders and commissions",
  "dashboard_subtitle_simple": "This is a minimal dashboard to test for errors.",

  // ===========================================
  // USER PROFILE & SETTINGS
  // ===========================================
  "profile": "Profile",
  "settings": "Settings",
  "pricing": "Pricing",
  "faq": "FAQ",
  "account_settings": "Account Settings",
  "user_profile": "User Profile",
  "user_info": "User Information",
  "user_name": "Name",
  "user_email": "Email",
  "user_role": "Role",
  "user_status": "Status",
  "user_kyc_status": "KYC Status",
  "user_created_at": "Member Since",
  "user_permissions": "Permissions",

  // ===========================================
  // PROFILE PAGE
  // ===========================================
  "profile": "Profile",
  "teams": "Teams",
  "projects": "Projects",
  "connections": "Connections",
  "about": "About",
  "contacts": "Contacts",
  "overview": "Overview",
  "profile_information": "Profile Information",
  "profile_image": "Profile Image",
  "profile_image_url": "Profile Image URL",
  "enter_profile_image_url": "Enter profile image URL",
  "upload_image": "Upload Image",
  "change_image": "Change Image",
  "upload_image_hint": "Allowed JPG, GIF or PNG. Max size of 5MB",
  "remove": "Remove",

  // ===========================================
  // LANGUAGE & LOCALIZATION
  // ===========================================
  "language": "Language",
  "english": "English",
  "french": "French",
  "arabic": "Arabic",
  "Level 3.1": "Level 3.1",
  "Level 3.2": "Level 3.2",
  "Raise Support": "Raise Support",
  "Documentation": "Documentation",
  "Dashboards": "Dashboards",
  "Analytics": "Analytics",
  "Apps & Pages": "Apps & Pages",
  "Email": "Email",
  "Chat": "Chat",
  "Invoice": "Invoice",
  "Preview": "Preview",
  "Add": "Add",
  "User": "User",
  "View": "View",
  "Login v1": "Login v1",
  "Login v2": "Login v2",
  "Login": "Login",
  "Register v1": "Register v1",
  "Register v2": "Register v2",
  "Register": "Register",
  "Forget Password v1": "Forget Password v1",
  "Forget Password v2": "Forget Password v2",
  "Forgot Password v1": "Forgot Password v1",
  "Forgot Password v2": "Forgot Password v2",
  "Forgot Password": "Forgot Password",
  "Reset Password v1": "Reset Password v1",
  "Reset Password v2": "Reset Password v2",
  "Reset Password": "Reset Password",
  "Miscellaneous": "Miscellaneous",
  "Coming Soon": "Coming Soon",
  "Not Authorized": "Not Authorized",
  "Under Maintenance": "Under Maintenance",
  "Error": "Error",
  "Statistics": "Statistics",
  "Actions": "Actions",
  "Access Control": "Access Control",
  "User Interface": "User Interface",
  "CRM": "CRM",
  "eCommerce": "eCommerce",
  "Icons": "Icons",
  "Chip": "Chip",
  "Dialog": "Dialog",
  "Expansion Panel": "Expansion Panel",
  "Combobox": "Combobox",
  "Textfield": "Textfield",
  "Range Slider": "Range Slider",
  "Menu": "Menu",
  "Snackbar": "Snackbar",
  "Tabs": "Tabs",
  "Form Elements": "Form Elements",
  "Form Layouts": "Form Layouts",
  "Authentication": "Authentication",
  "Page Not Found - 404": "Page Not Found - 404",
  "Not Authorized - 401": "Not Authorized - 401",
  "Server Error - 500": "Server Error - 500",
  "2": "2",
  "Forms": "Forms",
  "Timeline": "Timeline",
  "Disabled Menu": "Disabled Menu",
  "Help Center": "Help Center",
  "Verify Email": "Verify Email",
  "Verify Email v1": "Verify Email v1",
  "Verify Email v2": "Verify Email v2",
  "Two Steps": "Two Steps",
  "Two Steps v1": "Two Steps v1",
  "Two Steps v2": "Two Steps v2",
  "Custom Input": "Custom Input",
  "Extensions": "Extensions",
  "Tour": "Tour",
  "Register Multi-Steps": "Register Multi-Steps",
  "Wizard Examples": "Wizard Examples",
  "Checkout": "Checkout",
  "Create Deal": "Create Deal",
  "Property Listing": "Property Listing",
  "Roles & Permissions": "Roles & Permissions",
  "Roles": "Roles",
  "Simple Table": "Simple Table",
  "Tables": "Tables",
  "Data Table": "Data Table",
  "Permissions": "Permissions",
  "Apps": "Apps",
  "Misc": "Misc",
  "Wizard Pages": "Wizard Pages",
  "Form Wizard": "Form Wizard",
  "Numbered": "Numbered",
  "3": "3",
  "ecommerce": "ecommerce",
  "Ecommerce": "Ecommerce",
  "Editors": "Editors",
  "Front Pages": "Front Pages",
  "Landing": "Landing",
  "checkout": "checkout",
  "Payment": "Payment",
  "Swiper": "Swiper",
  "Product": "Product",
  "Category": "Category",
  "Order": "Order",
  "Details": "Details",
  "Customer": "Customer",
  "Manage Review": "Manage Review",
  "Referrals": "Referrals",
  "Settings": "Settings",
  "Overview": "Overview",
  "My Course": "My Course",
  "Course Details": "Course Details",
  "Academy": "Academy",
  "Logistics": "Logistics",
  "Dashboard": "Dashboard",
  "Fleet": "Fleet",
  "5": "5",
  "10": "10",
  "20": "20",
  "25": "25",
  "50": "50",
  "100": "100",
  "Affiliate Platform": "Affiliate Platform",
  "Affiliates": "Affiliates",
  "Orders": "Orders",
  "Commissions": "Commissions",
  "Products": "Products",
  "Offers": "Offers",
  "Payments": "Payments",
  "Reports": "Reports",
  "Marketing Materials": "Marketing Materials",
  "Create Order": "Create Order",
  "Order History": "Order History",
  "Commission Rate": "Commission Rate",
  "Total Earnings": "Total Earnings",
  "Pending Orders": "Pending Orders",
  "Delivered Orders": "Delivered Orders",
  "Cancelled Orders": "Cancelled Orders",
  "Affiliate Dashboard": "Affiliate Dashboard",
  "Admin Dashboard": "Admin Dashboard",
  "Manage Affiliates": "Manage Affiliates",
  "Manage Products": "Manage Products",
  "Order Management": "Order Management",
  "Payment Management": "Payment Management",
  "Welcome": "Welcome",
  "Logout": "Logout",
  "$vuetify": {
    "badge": "Badge",
    "noDataText": "No data available",
    "close": "Close",
    "open": "open",
    "loading": "loading",
    "carousel": {
      "ariaLabel": {
        "delimiter": "delimiter"
      }
    },
    "dataFooter": {
      "itemsPerPageText": "Items per page:",
      "itemsPerPageAll": "All",
      "pageText": "{0}-{1} of {2}",
      "firstPage": "First Page",
      "prevPage": "Previous Page",
      "nextPage": "Next Page",
      "lastPage": "Last Page"
    },
    "pagination": {
      "ariaLabel": {
        "root": "root",
        "previous": "previous",
        "first": "first",
        "last": "last",
        "next": "next",
        "currentPage": "currentPage",
        "page": "page"
      }
    },
    "input": {
      "clear": "clear",
      "appendAction": "appendAction",
      "prependAction": "prependAction",
      "counterSize": "counterSize",
      "otp": "otp"
    },
    "fileInput": {
      "counterSize": "counterSize"
    },
    "rating": {
      "ariaLabel": {
        "item": "item"
      }
    }
  },

  "user_management": "User Management",
  "manage_all_users": "Manage all users in the system",
  "add_user": "Add User",
  "create_new_user": "Create New User",
  "edit_user": "Edit User",
  "create_user": "Create User",
  "update_user": "Update User",
  "name": "Name",
  "email": "Email",
  "role": "Role",
  "status": "Status",
  "kyc_status": "KYC Status",
  "created": "Created",
  "actions": "Actions",
  "full_name": "Full Name",
  "enter_full_name": "Enter full name",
  "enter_email": "Enter email address",
  "password": "Password",
  "enter_password": "Enter password",
  "select_role": "Select role",
  "select_status": "Select status",
  "cancel": "Cancel",
  "all_status": "All Status",
  "active": "Active",
  "inactive": "Inactive",
  "suspended": "Suspended",
  "loading_users": "Loading users",
  "no_users_found": "No users found",
  "try_adjusting_search": "Try adjusting your search criteria",
  "confirm_delete_user": "Are you sure you want to delete {name}?",
  "search_users": "Search users...",
  "confirm_delete": "Confirm Delete",
  "user_created_successfully": "User {name} created successfully!",
  "user_updated_successfully": "User {name} updated successfully!",
  "user_deleted_successfully": "User {name} deleted successfully!",
  "failed_to_load_users": "Failed to load users",
  "failed_to_create_user": "Failed to create user",
  "failed_to_update_user": "Failed to update user",
  "failed_to_delete_user": "Failed to delete user",
  "user_status_updated_successfully": "User status updated successfully",

  "role_management": "Role & Permission Management",
  "manage_roles_permissions": "Manage roles and permissions in the system",
  "add_role": "Add Role",
  "add_permission": "Add Permission",
  "create_new_role": "Create New Role",
  "edit_role": "Edit Role",
  "create_new_permission": "Create New Permission",
  "role_name": "Role Name",
  "enter_role_name": "Enter role name",
  "permission_name": "Permission Name",
  "enter_permission_name": "Enter permission name",
  "description": "Description",
  "permissions": "Permissions",
  "users_count": "Users Count",
  "assign_permissions": "Assign Permissions",
  "create_role": "Create Role",
  "update_role": "Update Role",
  "create_permission": "Create Permission",
  "confirm_delete_role": "Are you sure you want to delete the role {name}?",
  "confirm_delete_permission": "Are you sure you want to delete the permission {name}?",
  "no_roles_found": "No roles found",
  "no_permissions_found": "No permissions found",
  "role_created_successfully": "Role {name} created successfully!",
  "role_updated_successfully": "Role {name} updated successfully!",
  "role_deleted_successfully": "Role {name} deleted successfully!",
  "permission_created_successfully": "Permission {name} created successfully!",
  "permission_deleted_successfully": "Permission {name} deleted successfully!",
  "failed_to_load_roles": "Failed to load roles",
  "failed_to_create_role": "Failed to create role",
  "failed_to_update_role": "Failed to update role",
  "failed_to_delete_role": "Failed to delete role",

  "kyc_documents": "KYC Documents",
  "manage_kyc_documents_desc": "Manage user KYC documents and verification status",
  "upload_document": "Upload Document",
  "upload_kyc_document": "Upload KYC Document",
  "document_type": "Document Type",
  "document_info": "Document Information",
  "user_id": "User ID",
  "enter_user_id": "Enter user ID",
  "select_user": "Select User",
  "choose_user": "Choose a user",
  "filter_by_user": "Filter by User",
  "no_users_found": "No users found",
  "select_file": "Select File",
  "supported_formats": "Supported formats",
  "max_size": "Max size",
  "upload": "Upload",
  "review": "Review",
  "review_document": "Review Document",
  "current_status": "Current Status",
  "new_status": "New Status",
  "rejection_reason": "Rejection Reason",
  "enter_rejection_reason": "Enter rejection reason",
  "download": "Download",
  "view": "View",
  "user": "User",
  "type": "Type",
  "uploaded": "Uploaded",
  "refresh": "Refresh",
  "no_documents_found": "No documents found",
  "no_documents_desc": "No KYC documents have been uploaded yet",
  "error_loading_documents": "Error loading documents",
  "try_again": "Try Again",
  "document_uploaded_successfully": "Document uploaded successfully",
  "document_reviewed_successfully": "Document reviewed successfully",
  "document_deleted_successfully": "Document deleted successfully",
  "failed_to_upload_document": "Failed to upload document",
  "failed_to_review_document": "Failed to review document",
  "failed_to_delete_document": "Failed to delete document",
  "confirm_delete_document": "Are you sure you want to delete this document?",
  "confirm_delete_document_desc": "Are you sure you want to delete the {type} document for {user}? This action cannot be undone.",
  "search_users": "Search users",
  "all_types": "All Types",
  "pending": "Pending",
  "approved": "Approved",
  "rejected": "Rejected",
  "not_required": "Not Required",
  "verified": "Verified",
  "not_verified": "Not Verified",
  "blocked": "Blocked",
  "phone": "Phone",
  "address": "Address",
  "enter_phone": "Enter phone number",
  "enter_address": "Enter address",

  "kyc_documents": "KYC Documents",
  "manage_kyc_documents_desc": "Manage user KYC documents and verification status",
  "upload_document": "Upload Document",
  "upload_kyc_document": "Upload KYC Document",
  "document_type": "Document Type",
  "document_info": "Document Information",
  "user_id": "User ID",
  "enter_user_id": "Enter user ID",
  "select_user": "Select User",
  "choose_user": "Choose a user",
  "filter_by_user": "Filter by User",
  "no_users_found": "No users found",
  "select_file": "Select File",
  "supported_formats": "Supported formats",
  "max_size": "Max size",
  "upload": "Upload",
  "review": "Review",
  "review_document": "Review Document",
  "current_status": "Current Status",
  "new_status": "New Status",
  "rejection_reason": "Rejection Reason",
  "enter_rejection_reason": "Enter rejection reason",
  "download": "Download",
  "view": "View",
  "user": "User",
  "type": "Type",
  "uploaded": "Uploaded",
  "refresh": "Refresh",
  "no_documents_found": "No documents found",
  "no_documents_desc": "No KYC documents have been uploaded yet",
  "error_loading_documents": "Error loading documents",
  "try_again": "Try Again",
  "document_uploaded_successfully": "Document uploaded successfully",
  "document_reviewed_successfully": "Document reviewed successfully",
  "document_deleted_successfully": "Document deleted successfully",
  "failed_to_upload_document": "Failed to upload document",
  "failed_to_review_document": "Failed to review document",
  "failed_to_delete_document": "Failed to delete document",
  "confirm_delete_document": "Are you sure you want to delete this document?",
  "confirm_delete_document_desc": "Are you sure you want to delete the {type} document for {user}? This action cannot be undone.",

  "nav_dashboard": "Dashboard",
  "nav_user_management": "User Management",
  "nav_all_users": "All Users",
  "nav_roles_permissions": "Roles & Permissions",
  "nav_kyc_documents": "KYC Documents",
  "nav_affiliate_management": "Affiliate Management",
  "nav_all_affiliates": "All Affiliates",
  "nav_affiliate_tiers": "Affiliate Tiers",
  "nav_order_management": "Order Management",
  "nav_all_orders": "All Orders",
  "nav_order_conflicts": "Order Conflicts",
  "nav_product_management": "Product Management",
  "nav_products": "Products",
  "nav_categories": "Categories",
  "nav_boutiques": "Boutiques",
  "nav_financial_management": "Financial Management",
  "nav_commissions": "Commissions",
  "nav_payments": "Payments",
  "nav_reports_analytics": "Reports & Analytics",
  "nav_sales_reports": "Sales Reports",
  "nav_affiliate_performance": "Affiliate Performance",
  "nav_my_orders": "My Orders",
  "nav_my_commissions": "My Commissions",
  "nav_marketing_materials": "Marketing Materials",

  "login_title": "Welcome to {title}! 👋🏻",
  "login_subtitle": "Please sign-in to your account and start the adventure",
  "demo_credentials": "Demo Credentials:",
  "admin_credentials": "Admin: admin / password",
  "affiliate_credentials": "Affiliate: affiliate / password",
  "email_or_username": "Email or Username",
  "email_placeholder": "Enter your email",
  "password_placeholder": "············",
  "remember_me": "Remember me",
  "forgot_password": "Forgot Password?",
  "login_button": "Login",
  "login_failed": "Login failed",
  "new_on_platform": "New on our platform?",
  "create_account": "Create an account",
  "or": "or",

  "profile": "Profile",
  "settings": "Settings",
  "pricing": "Pricing",
  "faq": "FAQ",
  "logout": "Logout",
  "login": "Login",
  "filter_by_status": "Filter by status",
  "clear": "Clear",
  "no_role": "No Role",

  "error_generic": "An error occurred",
  "error_validation_failed": "Validation failed",
  "error_conflict": "Conflict occurred",
  "error_authentication_required": "Authentication required",
  "error_access_forbidden": "Access forbidden",
  "error_resource_not_found": "Resource not found",
  "error_server_error": "Server error ({status})",
  "error_network": "Network error",

  "language": "Language",
  "english": "English",
  "french": "French",
  "arabic": "Arabic",

  // ===========================================
  // ERROR MESSAGES
  // ===========================================
  "error_generic": "An error occurred",
  "error_validation_failed": "Validation failed",
  "error_conflict": "Conflict occurred",
  "error_authentication_required": "Authentication required",
  "error_access_forbidden": "Access forbidden",
  "error_resource_not_found": "Resource not found",
  "error_server_error": "Server error ({status})",
  "error_network": "Network error",
  "error_login_failed": "Login failed",
  "error_invalid_credentials": "The provided credentials are incorrect",

  // ===========================================
  // SUCCESS MESSAGES
  // ===========================================
  "success_login": "Login successful",
  "success_logout": "Logout successful",
  "success_registration": "Registration successful",
  "success_user_created": "User created successfully",
  "success_user_updated": "User updated successfully",
  "success_user_deleted": "User deleted successfully",
  "success_role_created": "Role created successfully",
  "success_role_updated": "Role updated successfully",
  "success_role_deleted": "Role deleted successfully",
  "success_permission_created": "Permission created successfully",
  "success_permission_deleted": "Permission deleted successfully",
  "success_document_uploaded": "Document uploaded successfully",
  "success_document_updated": "Document updated successfully",
  "success_document_deleted": "Document deleted successfully",

  // ===========================================
  // CONFIRMATION MESSAGES
  // ===========================================
  "confirm_delete": "Confirm Delete",
  "confirm_delete_user": "Are you sure you want to delete {name}?",
  "confirm_delete_role": "Are you sure you want to delete the role '{name}'?",
  "confirm_delete_permission": "Are you sure you want to delete the permission '{name}'?",
  "confirm_delete_document": "Are you sure you want to delete this document?",
  "confirm_delete_document_desc": "Are you sure you want to delete the {type} document for {user}? This action cannot be undone.",

  // ===========================================
  // VALIDATION MESSAGES
  // ===========================================
  "validation_required": "This field is required",
  "validation_email": "Please enter a valid email address",
  "validation_min_length": "Must be at least {min} characters",
  "validation_max_length": "Must not exceed {max} characters",
  "validation_password_mismatch": "Passwords do not match",
  "validation_unique": "This value is already taken",
  "validation_between": "Enter number between {min} and {max}",
  "validation_integer": "This field must be an integer",
  "validation_format_invalid": "The format is invalid",
  "validation_alpha_only": "This field may only contain alphabetic characters",
  "validation_url_invalid": "URL is invalid",
  "validation_exact_length": "Must be exactly {length} characters",
  "validation_alpha_dash_only": "This field may only contain letters, numbers, dashes and underscores",

  // ===========================================
  // EMPTY STATES & PLACEHOLDERS
  // ===========================================
  "empty_no_data": "No data available",
  "empty_no_users": "No users found",
  "empty_no_roles": "No roles found",
  "empty_no_permissions": "No permissions found",
  "empty_no_documents": "No documents found",
  "empty_no_orders": "No orders found",
  "loading": "Loading...",
  "no_role": "No Role",

  // ===========================================
  // PAGINATION & FILTERS
  // ===========================================
  "pagination_showing": "Showing {from} to {to} of {total} entries",
  "pagination_previous": "Previous",
  "pagination_next": "Next",
  "filter_all": "All",
  "filter_clear": "Clear Filters",

  // ===========================================
  // DOCUMENT TYPES & KYC
  // ===========================================
  "doc_type_cni": "National ID",
  "doc_type_passport": "Passport",
  "doc_type_rib": "Bank Statement",
  "doc_type_contrat": "Contract",
  "kyc_status_non_requis": "Not Required",
  "kyc_status_en_attente": "Pending",
  "kyc_status_valide": "Valid",
  "kyc_status_refuse": "Rejected",

  // ===========================================
  // BACKEND API MESSAGES
  // ===========================================
  "api_access_denied_admin": "Access denied. Admin role required.",
  "api_access_denied_affiliate": "Access denied. Affiliate role required.",
  "api_access_denied_permission": "Access denied. \"{permission}\" permission required.",
  "api_welcome_admin": "Welcome to Admin Dashboard",
  "api_welcome_affiliate": "Welcome to Affiliate Dashboard",
  "api_user_management_panel": "User Management Panel",
  "api_order_creation_form": "Order creation form",
  "api_access_denied_no_role": "Access denied. No valid role assigned.",

  // ===========================================
  // AFFILIATE DASHBOARD SPECIFIC
  // ===========================================
  "affiliate_dashboard_title": "Affiliate Dashboard",
  "welcome_message": "Welcome",
  "my_orders_count": "My Orders",
  "pending_orders_count": "Pending Orders",
  "total_earnings": "Total Earnings",
  "this_month_earnings": "This Month Earnings",
  "quick_actions": "Quick Actions",
  "create_order": "Create Order",
  "order_history": "Order History",
  "view_commissions": "Commissions",
  "view_marketing_materials": "Marketing Materials",
  "user_info": "User Information",
  "user_name": "Name",
  "user_email": "Email",
  "user_role": "Role",
  "user_status": "Status",
  "user_kyc_status": "KYC Status",
  "user_created_at": "Member Since",

  "admin_boutiques_title": "Boutiques",
  "admin_boutiques_description": "Manage boutiques and their details",
  "admin_boutiques_list": "Boutiques List",
  "admin_boutiques_create": "Create New Boutique",
  "admin_boutiques_edit": "Edit Boutique",
  "admin_boutiques_show": "Boutique Details",
  "admin_boutiques_delete": "Delete Boutique",
  "admin_boutiques_delete_confirm": "Are you sure you want to delete this boutique?",
  "admin_boutiques_name": "Boutique Name",
  "admin_boutiques_description_field": "Description",
  "admin_boutiques_url": "Boutique URL",
  "admin_boutiques_logo": "Logo",
  "admin_boutiques_status": "Status",
  "admin_boutiques_category": "Category",
  "admin_boutiques_commission_rate": "Commission Rate (%)",
  "admin_boutiques_contact_email": "Contact Email",
  "admin_boutiques_phone": "Phone Number",
  "admin_boutiques_address": "Address",
  "admin_boutiques_country": "Country",
  "admin_boutiques_city": "City",
  "admin_boutiques_postal_code": "Postal Code",
  "admin_boutiques_list_title": "Boutiques List",
  "admin_boutiques_create_title": "Add New Boutique", 
  "admin_boutiques_edit_title": "Edit Boutique",
  "admin_boutiques_view_title": "View Boutique",
  "admin_boutiques_delete_title": "Delete Boutique",
  "admin_boutiques_slug": "Slug",
  "admin_boutiques_slug_hint": "Auto-generated from name if empty",
  "admin_boutiques_email": "Email",
  "admin_boutiques_website": "Website",
  "admin_boutiques_owner": "Owner",
  "admin_boutiques_general_info": "General Information",
  "admin_boutiques_owner_info": "Owner Information", 
  "admin_boutiques_contact_info": "Contact Information",
  "admin_boutiques_metadata": "Metadata",
  "admin_boutiques_search_placeholder": "Search boutiques...",
  "admin_boutiques_no_results": "No boutiques found",
  "admin_boutiques_created_successfully": "Boutique created successfully",
  "admin_boutiques_updated_successfully": "Boutique updated successfully",
  "admin_boutiques_deleted_successfully": "Boutique deleted successfully",
  "admin_boutiques_error_loading": "Error loading boutiques",
  "admin_boutiques_error_creating": "Error creating boutique",
  "admin_boutiques_error_updating": "Error updating boutique",
  "admin_boutiques_error_deleting": "Error deleting boutique",
  "admin_boutiques_bulk_actions": "Bulk Actions",
  "admin_boutiques_bulk_delete": "Delete Selected",
  "admin_boutiques_bulk_export": "Export Selected",
  "admin_boutiques_export_all": "Export All",
  "admin_boutiques_import": "Import",
  "admin_boutiques_filters": "Filters",
  "admin_boutiques_sort_by": "Sort By",
  "admin_boutiques_sort_name_asc": "Name (A-Z)",
  "admin_boutiques_sort_name_desc": "Name (Z-A)",
  "admin_boutiques_sort_created_asc": "Oldest First",
  "admin_boutiques_sort_created_desc": "Newest First",
  "admin_boutiques_filter_status_all": "All Statuses",
  "admin_boutiques_filter_status_active": "Active",
  "admin_boutiques_filter_status_inactive": "Inactive",
  "admin_boutiques_filter_status_pending": "Pending",
  "admin_boutiques_filter_category_all": "All Categories",
  "admin_boutiques_pagination_info": "Showing {from} to {to} of {total} boutiques",
  "admin_boutiques_items_per_page": "Items per page",

  // ===========================================
  // ADMIN CATEGORIES MODULE
  // ===========================================
  "admin_categories_title": "Categories",
  "admin_categories_list": "Categories List",
  "admin_categories_create": "Create Category",
  "admin_categories_edit": "Edit Category",
  "admin_categories_update": "Update Category",
  "admin_categories_view": "View Category",
  "admin_categories_delete": "Delete Category",
  "admin_categories_delete_title": "Delete Category",
  "admin_categories_delete_confirm": "Are you sure you want to delete the category '{name}'? This action cannot be undone.",
  "admin_categories_name": "Name",
  "admin_categories_slug": "Slug",
  "admin_categories_description": "Description",
  "admin_categories_image": "Image",
  "admin_categories_order": "Order",
  "admin_categories_status": "Status",
  "admin_categories_created_at": "Created At",
  "admin_categories_updated_at": "Updated At",
  "admin_categories_total": "Total Categories",
  "admin_categories_active": "Active Categories",
  "admin_categories_inactive": "Inactive Categories",
  "admin_categories_add_new": "Add New Category",
  "admin_categories_select_image": "Select Image",
  "admin_categories_change_image": "Change Image",
  "admin_categories_image_help": "Maximum size: 2MB. Supported formats: JPG, PNG, GIF",
  "admin_categories_slug_help": "URL-friendly version of the name. Only lowercase letters, numbers, and hyphens allowed.",
  "admin_categories_active_help": "Category is visible and can be used for products",
  "admin_categories_inactive_help": "Category is hidden and cannot be used for products",
  "admin_categories_additional_info": "Additional Information",
  "admin_categories_products_count": "Products Count",

  // ===========================================
  // ADMIN PRODUCTS MODULE
  // ===========================================
  "admin_produits_title": "Products",
  "admin_produits_subtitle": "Manage your product catalog",
  "admin_produits_list": "Products List",
  "admin_produits_create": "Create Product",
  "admin_produits_create_subtitle": "Add a new product to your catalog",
  "admin_produits_edit": "Edit Product",
  "admin_produits_edit_subtitle": "Update product information",
  "admin_produits_show": "Product Details",
  "admin_produits_show_subtitle": "View product information",
  "admin_produits_update": "Update Product",
  "admin_produits_view": "View Product",
  "admin_produits_delete": "Delete Product",
  "admin_produits_delete_title": "Delete Product",
  "admin_produits_delete_message": "Are you sure you want to delete this product? This action cannot be undone.",
  "admin_produits_delete_confirm": "Are you sure you want to delete the product '{name}'? This action cannot be undone.",
  "admin_produits_basic_info": "Basic Information",
  "admin_produits_pricing": "Pricing & Stock",
  "admin_produits_boutique": "Boutique",
  "admin_produits_categorie": "Category",
  "admin_produits_titre": "Product Title",
  "admin_produits_titre_placeholder": "Enter product title",
  "admin_produits_name": "Product Name",
  "admin_produits_slug": "Slug",
  "admin_produits_description": "Description",
  "admin_produits_description_placeholder": "Describe your product",
  "admin_produits_prix_achat": "Purchase Price",
  "admin_produits_prix_achat_placeholder": "Cost price",
  "admin_produits_prix_vente": "Sale Price",
  "admin_produits_prix_vente_placeholder": "Selling price",
  "admin_produits_prix_affilie": "Affiliate Price",
  "admin_produits_prix_affilie_placeholder": "Price for affiliates",
  "admin_produits_price_purchase": "Purchase Price",
  "admin_produits_price": "Sale Price",
  "admin_produits_status": "Status",
  "admin_produits_quantite_min": "Minimum Quantity",
  "admin_produits_quantite_min_placeholder": "Minimum order quantity",
  "admin_produits_quantity_min": "Minimum Quantity",
  "admin_produits_notes_admin": "Admin Notes",
  "admin_produits_notes_admin_placeholder": "Internal notes for administrators",
  "admin_produits_notes": "Admin Notes",
  "admin_produits_actif": "Active Product",
  "admin_produits_active_help": "This product is visible and available for purchase",
  "admin_produits_inactive_help": "This product is hidden and not available for purchase",
  "admin_produits_search_placeholder": "Search products...",
  "admin_produits_seo": "SEO & Metadata",
  "admin_produits_slug_placeholder": "product-url-slug",
  "admin_produits_meta_description": "Meta Description",
  "admin_produits_meta_description_placeholder": "Brief description for search engines",
  "admin_produits_meta_keywords": "Meta Keywords",
  "admin_produits_meta_keywords_placeholder": "keyword1, keyword2, keyword3",
  "admin_produits_features": "Product Features",
  "admin_produits_features_help": "List the key features and benefits of your product",
  "admin_produits_features_list": "Features List",
  "admin_produits_features_placeholder": "• High quality materials\n• Fast shipping\n• 1 year warranty",
  "admin_produits_preview": "Product Preview",
  "admin_produits_preview_empty": "Fill in the product details to see a preview",
  "admin_produits_tips": "Tips & Guidelines",
  "admin_produits_tip_title": "Use clear, descriptive titles",
  "admin_produits_tip_description": "Write detailed descriptions",
  "admin_produits_tip_pricing": "Set competitive prices",
  "admin_produits_tip_images": "Add high-quality images",
  "admin_produits_create_help": "After creating the product, you can add images, videos, and variants",
  "admin_produits_images": "Images",
  "admin_produits_videos": "Videos",
  "admin_produits_variantes": "Variants",
  "admin_produits_images_management": "Images Management",
  "admin_produits_videos_management": "Videos Management",
  "admin_produits_variants_management": "Variants Management",
  "admin_produits_features_management": "Features Management",
  "admin_produits_add_image": "Add Image",
  "admin_produits_add_video": "Add Video",
  "admin_produits_add_variant": "Add Variant",
  "admin_produits_add_feature": "Add Feature",
  "admin_produits_no_images": "No images added yet",
  "admin_produits_no_videos": "No videos added yet",
  "admin_produits_no_variants": "No variants added yet",
  "admin_produits_image_alt": "Alt Text",
  "admin_produits_video_url": "Video URL",
  "admin_produits_video_title": "Video Title",
  "admin_produits_variant_name": "Variant Name",
  "admin_produits_variant_value": "Variant Value",
  "admin_produits_variant_price": "Variant Price",
  "admin_produits_variant_sku": "SKU",
  "admin_produits_feature": "Feature",
  "admin_produits_propositions": "Affiliate Propositions",
  "admin_produits_videos_help": "Add videos to showcase your product features and demonstrations",
  "admin_produits_variants_help": "Create different versions of your product (colors, sizes, materials, etc.)",
  "admin_produits_features_help": "List the key features and benefits that make your product special",
  "admin_produits_features_examples": "Examples: High quality materials, Fast shipping, 1 year warranty, Eco-friendly, etc.",
  "admin_produits_feature_placeholder": "Enter a product feature or benefit",
  "admin_produits_features_preview": "Features Preview",
  "admin_produits_no_features_preview": "Add features to see how they will appear to customers",
  "admin_produits_video_order": "Order",
  "admin_produits_video_preview": "Video Preview",
  "admin_produits_video_url_invalid": "Please enter a valid YouTube or Vimeo URL",
  "admin_produits_variant": "Variant",
  "admin_produits_variant_image": "Variant Image",
  "admin_produits_variant_stock": "Stock",
  "admin_produits_propositions_management": "Affiliate Propositions Management",
  "admin_produits_propositions_help": "Create commission offers for affiliates to promote this product",
  "admin_produits_add_proposition": "Add Proposition",
  "admin_produits_no_propositions": "No affiliate propositions created yet",
  "admin_produits_proposition": "Proposition",
  "admin_produits_affiliate": "Affiliate",
  "admin_produits_commission_percentage": "Commission %",
  "admin_produits_commission_fixe": "Fixed Commission",
  "admin_produits_commission_invalid": "Commission must be between 0 and 100%",
  "admin_produits_date_debut": "Start Date",
  "admin_produits_date_fin": "End Date",
  "admin_produits_proposition_notes": "Notes",
  "admin_produits_commission_preview": "Commission Preview",
  "admin_produits_commission_preview_empty": "Enter sale price and commission to see preview",
  "admin_produits_quantite_min": "Minimum Quantity",
  "admin_produits_quantite_min_placeholder": "Enter minimum quantity required",
  "admin_produits_image": "Image",
  "admin_produits_image_preview": "Image Preview",
  "admin_produits_upload_image": "Upload Image",
  "admin_produits_image_url_placeholder": "https://example.com/image.jpg",
  "validation_required": "This field is required",
  "admin_produits_created_at": "Created At",
  "admin_produits_updated_at": "Updated At",
  "admin_produits_total": "Total Products",
  "admin_produits_active": "Active Products",
  "admin_produits_inactive": "Inactive Products",
  "admin_produits_add_new": "Add New Product",
  "admin_produits_details": "Details",
  "admin_produits_gallery": "Gallery",
  "admin_produits_upload_image": "Upload Image",
  "admin_produits_image_url": "Image URL",
  "admin_produits_image_order": "Display Order",
  "admin_produits_no_images": "No images uploaded",
  "admin_produits_drag_sort": "Drag to reorder images",
  "admin_produits_videos": "Product Videos",
  "admin_produits_add_video": "Add Video",
  "admin_produits_video": "Video",
  "admin_produits_no_title": "No title",
  "admin_produits_no_videos": "No videos yet",
  "admin_produits_no_videos_desc": "Add videos to showcase your product features and demonstrations",
  "admin_produits_add_first_video": "Add First Video",
  "admin_produits_edit_video": "Edit Video",
  "admin_produits_video_url": "Video URL",
  "admin_produits_video_url_placeholder": "https://www.youtube.com/watch?v=...",
  "admin_produits_video_title": "Video Title",
  "admin_produits_video_title_placeholder": "Product demonstration",
  "admin_produits_video_order": "Display Order",
  "admin_produits_video_preview": "Preview",
  "admin_produits_delete_video": "Delete Video",
  "admin_produits_delete_video_confirm": "Are you sure you want to delete this video?",
  "admin_produits_video_updated": "Video updated successfully",
  "admin_produits_video_added": "Video added successfully",
  "admin_produits_video_deleted": "Video deleted successfully",
  "admin_produits_variants": "Product Variants",
  "admin_produits_add_variant": "Add Variant",
  "admin_produits_no_variants": "No variants yet",
  "admin_produits_no_variants_desc": "Add variants like colors, sizes, or configurations for this product",
  "admin_produits_add_first_variant": "Add First Variant",
  "admin_produits_edit_variant": "Edit Variant",
  "admin_produits_variant_name": "Variant Type",
  "admin_produits_variant_name_placeholder": "Color, Size, Storage...",
  "admin_produits_variant_value": "Variant Value",
  "admin_produits_variant_value_placeholder": "Red, Large, 256GB...",
  "admin_produits_variant_price": "Variant Price",
  "admin_produits_variant_price_placeholder": "Leave empty to use default price",
  "admin_produits_variant_sku": "SKU Code",
  "admin_produits_variant_sku_placeholder": "PROD-COLOR-SIZE",
  "admin_produits_variant_active": "Active Variant",
  "admin_produits_default_price": "Default price",
  "admin_produits_delete_variant": "Delete Variant",
  "admin_produits_delete_variant_confirm": "Are you sure you want to delete this variant?",
  "admin_produits_variant_updated": "Variant updated successfully",
  "admin_produits_variant_added": "Variant added successfully",
  "admin_produits_variant_deleted": "Variant deleted successfully",

  "admin_produits_ruptures": "Stock Alerts",
  "admin_produits_add_rupture": "Add Stock Alert",
  "admin_produits_no_ruptures": "No stock alerts yet",
  "admin_produits_no_ruptures_desc": "Set up alerts when variants go out of stock",
  "admin_produits_add_first_rupture": "Add First Alert",
  "admin_produits_edit_rupture": "Edit Stock Alert",
  "admin_produits_rupture_variant": "Product Variant",
  "admin_produits_rupture_seuil": "Stock Threshold",
  "admin_produits_rupture_seuil_placeholder": "Alert when stock falls below...",
  "admin_produits_rupture_message": "Alert Message",
  "admin_produits_rupture_message_placeholder": "Custom message when out of stock",
  "admin_produits_rupture_active": "Active Alert",
  "admin_produits_delete_rupture": "Delete Alert",
  "admin_produits_delete_rupture_confirm": "Are you sure you want to delete this stock alert?",
  "admin_produits_rupture_updated": "Stock alert updated successfully",
  "admin_produits_rupture_added": "Stock alert added successfully",
  "admin_produits_rupture_deleted": "Stock alert deleted successfully",

  "admin_produits_propositions": "Product Propositions",
  "admin_produits_add_proposition": "Add Proposition",
  "admin_produits_no_propositions": "No propositions yet",
  "admin_produits_no_propositions_desc": "Add affiliate propositions for this product",
  "admin_produits_add_first_proposition": "Add First Proposition",
  "admin_produits_edit_proposition": "Edit Proposition",
  "admin_produits_proposition_titre": "Proposition Title",
  "admin_produits_proposition_titre_placeholder": "Enter proposition title",
  "admin_produits_proposition_description": "Description",
  "admin_produits_proposition_description_placeholder": "Describe the proposition",
  "admin_produits_proposition_commission": "Commission Rate",
  "admin_produits_proposition_commission_placeholder": "Commission percentage",
  "admin_produits_proposition_active": "Active Proposition",
  "admin_produits_delete_proposition": "Delete Proposition",
  "admin_produits_delete_proposition_confirm": "Are you sure you want to delete this proposition?",
  "admin_produits_proposition_updated": "Proposition updated successfully",
  "admin_produits_proposition_added": "Proposition added successfully",
  "admin_produits_proposition_deleted": "Proposition deleted successfully",

  "admin_produits_reviews": "Product Reviews",
  "admin_produits_add_review": "Add Review",
  "admin_produits_no_reviews": "No reviews yet",
  "admin_produits_no_reviews_desc": "Manage product reviews and ratings",
  "admin_produits_add_first_review": "Add First Review",
  "admin_produits_edit_review": "Edit Review",
  "admin_produits_review_rating": "Rating",
  "admin_produits_review_comment": "Review Comment",
  "admin_produits_review_comment_placeholder": "Customer review comment",
  "admin_produits_review_user": "Reviewer",
  "admin_produits_review_approved": "Approved Review",
  "admin_produits_delete_review": "Delete Review",
  "admin_produits_delete_review_confirm": "Are you sure you want to delete this review?",
  "admin_produits_review_updated": "Review updated successfully",
  "admin_produits_review_added": "Review added successfully",
  "admin_produits_review_deleted": "Review deleted successfully",

  "common_basic_info": "Basic Information",
  "common_pricing_stock": "Pricing & Stock",
  "common_metadata": "Metadata",
  "common_view_original": "View Original",
  "common_save_order": "Save Order",
  "common_slug_auto_generate": "Leave empty to auto-generate from name",

  "breadcrumb_home": "Home",
  "breadcrumb_admin": "Administration",
  "breadcrumb_create": "Create",
  "breadcrumb_edit": "Edit",
  "breadcrumb_view": "View",
  "common_create": "Create",
  "common_edit": "Edit",
  "common_view": "View",
  "common_delete": "Delete",
  "common.save": "Save",
  "common.cancel": "Cancel",
  "common_back": "Back",
  "common_search": "Search",
  "common_filter": "Filter",
  "common_reset": "Reset",
  "common_loading": "Loading...",
  "common_no_data": "No data available",
  "form_name": "Name",
  "common.created_at": "Created at",
  "common.updated_at": "Updated at",
  "common.close": "Close",
  "common.confirm": "Confirm",
  "common.sort.created_desc": "Newest first",
  "common.sort.name_asc": "Name (A–Z)",
  "common.sort.status": "Status",
  "common.upload": "Upload",
  "common.change": "Change",
  "common.remove": "Remove",
  "common_uploading": "Uploading",

  // ===========================================
  // MISSING TRANSLATION KEYS FROM CONSOLE WARNINGS
  // ===========================================

  // Direct French text keys (English translations)
  "Tableau de Bord": "Dashboard",
  "Gestion des Utilisateurs": "User Management",
  "Tous les Utilisateurs": "All Users",
  "Rôles et Permissions": "Roles & Permissions",
  "Documents KYC": "KYC Documents",
  "Gestion des Affiliés": "Affiliate Management",
  "Tous les Affiliés": "All Affiliates",
  "Niveaux d'Affiliation": "Affiliate Tiers",
  "Gestion des Commandes": "Order Management",
  "Toutes les Commandes": "All Orders",
  "Conflits de Commandes": "Order Conflicts",
  "Gestion des Produits": "Product Management",
  "Produits": "Products",
  "Catégories": "Categories",
  "Boutiques": "Boutiques",
  "Gestion Financière": "Financial Management",
  "Paiements": "Payments",
  "Rapports et Analyses": "Reports & Analytics",
  "Rapports de Ventes": "Sales Reports",
  "Performance des Affiliés": "Affiliate Performance",
  "Profil": "Profile",

  // Missing common.* keys
  "common.search": "Search",
  "common.all": "All",
  "common.active": "Active",
  "common.inactive": "Inactive",
  "common.status": "Status",
  "common.items_per_page": "Items per page",
  "common.actions": "Actions",
  "common.showing": "Showing",
  "common.to": "to",
  "common.of": "of",
  "common.results": "results",

  // Missing data availability key
  "Aucune donnée disponible": "No data available",

  // Missing $vuetify keys
  "$vuetify.dataIterator.loadingText": "Loading items..."
}
