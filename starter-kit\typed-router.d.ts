/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-router. ‼️ DO NOT MODIFY THIS FILE ‼️
// It's recommended to commit this file.
// Make sure to add this file to your tsconfig.json file as an "includes" or "files" entry.

declare module 'vue-router/auto-routes' {
  import type {
    RouteRecordInfo,
    ParamValue,
    ParamValueOneOrMore,
    ParamValueZeroOrMore,
    ParamValueZeroOrOne,
  } from 'unplugin-vue-router/types'

  /**
   * Route name map generated by unplugin-vue-router
   */
  export interface RouteNamedMap {
    'root': RouteRecordInfo<'root', '/', Record<never, never>, Record<never, never>>,
    '$error': RouteRecordInfo<'$error', '/:error(.*)', { error: ParamValue<true> }, { error: ParamValue<false> }>,
    'admin-affiliate-tiers': RouteRecordInfo<'admin-affiliate-tiers', '/admin/affiliate-tiers', Record<never, never>, Record<never, never>>,
    'admin-affiliates': RouteRecordInfo<'admin-affiliates', '/admin/affiliates', Record<never, never>, Record<never, never>>,
    'admin-boutiques': RouteRecordInfo<'admin-boutiques', '/admin/boutiques', Record<never, never>, Record<never, never>>,
    'admin-boutiques-create': RouteRecordInfo<'admin-boutiques-create', '/admin/boutiques/create', Record<never, never>, Record<never, never>>,
    'admin-categories': RouteRecordInfo<'admin-categories', '/admin/categories', Record<never, never>, Record<never, never>>,
    'admin-commissions': RouteRecordInfo<'admin-commissions', '/admin/commissions', Record<never, never>, Record<never, never>>,
    'admin-dashboard': RouteRecordInfo<'admin-dashboard', '/admin/dashboard', Record<never, never>, Record<never, never>>,
    'admin-dashboard-simple': RouteRecordInfo<'admin-dashboard-simple', '/admin/dashboard-simple', Record<never, never>, Record<never, never>>,
    'admin-kyc-documents': RouteRecordInfo<'admin-kyc-documents', '/admin/kyc-documents', Record<never, never>, Record<never, never>>,
    'admin-order-conflicts': RouteRecordInfo<'admin-order-conflicts', '/admin/order-conflicts', Record<never, never>, Record<never, never>>,
    'admin-orders': RouteRecordInfo<'admin-orders', '/admin/orders', Record<never, never>, Record<never, never>>,
    'admin-payments': RouteRecordInfo<'admin-payments', '/admin/payments', Record<never, never>, Record<never, never>>,
    'admin-products': RouteRecordInfo<'admin-products', '/admin/products', Record<never, never>, Record<never, never>>,
    'admin-produits': RouteRecordInfo<'admin-produits', '/admin/produits', Record<never, never>, Record<never, never>>,
    'admin-produits-id': RouteRecordInfo<'admin-produits-id', '/admin/produits/:id', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'admin-produits-id-edit': RouteRecordInfo<'admin-produits-id-edit', '/admin/produits/:id/edit', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'admin-produits-create': RouteRecordInfo<'admin-produits-create', '/admin/produits/Create', Record<never, never>, Record<never, never>>,
    'admin-reports-affiliates': RouteRecordInfo<'admin-reports-affiliates', '/admin/reports-affiliates', Record<never, never>, Record<never, never>>,
    'admin-reports-sales': RouteRecordInfo<'admin-reports-sales', '/admin/reports-sales', Record<never, never>, Record<never, never>>,
    'admin-roles': RouteRecordInfo<'admin-roles', '/admin/roles', Record<never, never>, Record<never, never>>,
    'admin-users': RouteRecordInfo<'admin-users', '/admin/users', Record<never, never>, Record<never, never>>,
    'admin-variants-attributs': RouteRecordInfo<'admin-variants-attributs', '/admin/variants/attributs', Record<never, never>, Record<never, never>>,
    'admin-variants-attributs-id-valeurs': RouteRecordInfo<'admin-variants-attributs-id-valeurs', '/admin/variants/attributs/:id/valeurs', { id: ParamValue<true> }, { id: ParamValue<false> }>,
    'affiliate-commissions': RouteRecordInfo<'affiliate-commissions', '/affiliate/commissions', Record<never, never>, Record<never, never>>,
    'affiliate-dashboard': RouteRecordInfo<'affiliate-dashboard', '/affiliate/dashboard', Record<never, never>, Record<never, never>>,
    'affiliate-marketing': RouteRecordInfo<'affiliate-marketing', '/affiliate/marketing', Record<never, never>, Record<never, never>>,
    'affiliate-orders': RouteRecordInfo<'affiliate-orders', '/affiliate/orders', Record<never, never>, Record<never, never>>,
    'login': RouteRecordInfo<'login', '/login', Record<never, never>, Record<never, never>>,
    'pslug': RouteRecordInfo<'pslug', '/p/:slug', { slug: ParamValue<true> }, { slug: ParamValue<false> }>,
    'profile': RouteRecordInfo<'profile', '/profile', Record<never, never>, Record<never, never>>,
    'test-auth': RouteRecordInfo<'test-auth', '/test-auth', Record<never, never>, Record<never, never>>,
    'unauthorized': RouteRecordInfo<'unauthorized', '/unauthorized', Record<never, never>, Record<never, never>>,
  }
}
